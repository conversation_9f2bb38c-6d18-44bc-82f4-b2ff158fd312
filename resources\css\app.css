@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    :root {
        --color-primary: #007BCE;
        --color-primary-dark: #005B99;
        --color-primary-light: #3CB5F9;
        --color-accent: #00CFFF;
        --color-sidebar-start: #002D4D;
        --color-sidebar-end: #007BCE;
        --color-bg-light: #F5F8FA;
        --color-text-primary: #1A1A1A;
        --color-text-secondary: #5A5A5A;
    }

    body {
        font-family: 'Inter', ui-sans-serif, system-ui, sans-serif;
    }
}

@layer utilities {
    .bg-primary {
        background-color: var(--color-primary);
    }
    .bg-primary-dark {
        background-color: var(--color-primary-dark);
    }
    .bg-primary-light {
        background-color: var(--color-primary-light);
    }
    .bg-accent {
        background-color: var(--color-accent);
    }
    .bg-sidebar-gradient {
        background: linear-gradient(135deg, var(--color-sidebar-start) 0%, var(--color-sidebar-end) 100%);
    }

    .text-primary {
        color: var(--color-primary);
    }
    .text-primary-dark {
        color: var(--color-primary-dark);
    }
    .text-accent {
        color: var(--color-accent);
    }

    .border-primary {
        border-color: var(--color-primary);
    }

    .hover\:bg-accent:hover {
        background-color: var(--color-accent);
    }
    .hover\:text-accent:hover {
        color: var(--color-accent);
    }
    .hover\:bg-primary-dark:hover {
        background-color: var(--color-primary-dark);
    }

    /* Custom select styling for better dropdown appearance */
    .select-improved {
        min-width: 180px;
        padding-right: 2.5rem !important;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
        background-position: right 0.75rem center;
        background-repeat: no-repeat;
        background-size: 1.5em 1.5em;
        appearance: none;
    }

    /* AI Enhanced Description Styling */
    .ai-enhanced-description {
        background-color: #f8f9fa;
        border-left: 4px solid var(--color-primary);
        border-radius: 0.5rem;
        padding: 1rem;
        margin-top: 0.75rem;
        font-style: italic;
        line-height: 1.6;
    }

    .ai-enhanced-header {
        color: var(--color-primary);
        font-weight: 600;
        font-size: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .ai-enhanced-content {
        color: #374151;
        white-space: pre-wrap;
        line-height: 1.7;
    }
}
